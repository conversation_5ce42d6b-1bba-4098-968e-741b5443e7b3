#!/usr/bin/env python3
"""
UNII Search MCP Server
基于 fastmcp 的 UNII 编号检索工具

功能：
1. 接收中文或英文名词输入
2. 使用 AI 生成最精准的英文检索词
3. 通过 FDA UNII Search API 获取 UNII 编号
4. 返回详细的搜索结果
"""

import asyncio
import json
import logging
import re
from typing import List, Dict, Any, Optional
from urllib.parse import quote_plus
import aiohttp
from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建 MCP 服务器实例
mcp = FastMCP("UNII Search Server")

# FDA UNII Search API 基础 URL
UNII_SEARCH_BASE_URL = "https://precision.fda.gov/uniisearch/srs/unii/"

class UNIISearcher:
    """UNII 搜索器类"""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'UNII-Search-MCP/1.0',
                'Accept': 'application/json',
                'Accept-Language': 'en-US,en;q=0.9'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    def generate_search_terms(self, input_term: str) -> List[str]:
        """
        生成英文检索词
        这里使用规则基础的方法，实际使用时可以集成 OpenAI API
        """
        # 清理输入
        term = input_term.strip()
        
        # 常见的中英文对照词典
        translation_dict = {
            # 常见药物成分
            "阿司匹林": "aspirin",
            "对乙酰氨基酚": "acetaminophen",
            "布洛芬": "ibuprofen",
            "青霉素": "penicillin",
            "胰岛素": "insulin",
            "肝素": "heparin",
            "维生素": "vitamin",
            "葡萄糖": "glucose",
            "氯化钠": "sodium chloride",
            "碳酸钙": "calcium carbonate",
            "硫酸镁": "magnesium sulfate",
            "氯化钾": "potassium chloride",
            "酒精": "alcohol",
            "苯甲醇": "benzyl alcohol",
            "聚维酮": "povidone",
            "聚乙二醇": "polyethylene glycol",
            "甘油": "glycerin",
            "丙二醇": "propylene glycol",
            # 常见化学物质
            "水": "water",
            "盐酸": "hydrochloric acid",
            "硫酸": "sulfuric acid",
            "硝酸": "nitric acid",
            "磷酸": "phosphoric acid",
            "醋酸": "acetic acid",
            "柠檬酸": "citric acid",
            "乳酸": "lactic acid",
        }
        
        search_terms = []
        
        # 如果是中文，尝试翻译
        if any('\u4e00' <= char <= '\u9fff' for char in term):
            if term in translation_dict:
                search_terms.append(translation_dict[term])
            else:
                # 如果没有直接翻译，添加原词用于搜索
                search_terms.append(term)
        else:
            # 英文输入，直接使用
            search_terms.append(term)
        
        # 生成变体
        base_term = search_terms[0] if search_terms else term
        
        # 添加常见变体
        variations = [
            base_term,
            base_term.lower(),
            base_term.upper(),
            base_term.title(),
        ]
        
        # 去重并返回
        return list(dict.fromkeys(variations))
    
    async def search_unii(self, search_term: str) -> Dict[str, Any]:
        """
        通过 FDA UNII Search API 搜索 UNII 编号
        """
        try:
            # 构建搜索 URL
            encoded_term = quote_plus(search_term)
            search_url = f"{UNII_SEARCH_BASE_URL}?query={encoded_term}&format=json"
            
            logger.info(f"搜索 UNII: {search_term} -> {search_url}")
            
            async with self.session.get(search_url) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "success": True,
                        "search_term": search_term,
                        "data": data,
                        "url": search_url
                    }
                else:
                    logger.warning(f"UNII 搜索失败: {response.status}")
                    return {
                        "success": False,
                        "search_term": search_term,
                        "error": f"HTTP {response.status}",
                        "url": search_url
                    }
        
        except Exception as e:
            logger.error(f"UNII 搜索异常: {e}")
            return {
                "success": False,
                "search_term": search_term,
                "error": str(e),
                "url": search_url if 'search_url' in locals() else None
            }
    
    def extract_unii_info(self, search_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从搜索结果中提取 UNII 信息
        """
        if not search_result.get("success") or not search_result.get("data"):
            return []
        
        data = search_result["data"]
        results = []
        
        # 处理不同的 API 响应格式
        if isinstance(data, dict):
            if "results" in data:
                items = data["results"]
            elif "items" in data:
                items = data["items"]
            else:
                items = [data]
        elif isinstance(data, list):
            items = data
        else:
            return []
        
        for item in items:
            if isinstance(item, dict):
                unii_info = {
                    "unii": item.get("unii", ""),
                    "preferred_name": item.get("preferredName", item.get("name", "")),
                    "display_name": item.get("displayName", ""),
                    "substance_class": item.get("substanceClass", ""),
                    "molecular_formula": item.get("molecularFormula", ""),
                    "molecular_weight": item.get("molecularWeight", ""),
                    "cas_number": item.get("casNumber", ""),
                    "search_term": search_result["search_term"]
                }
                
                # 只添加有 UNII 编号的结果
                if unii_info["unii"]:
                    results.append(unii_info)
        
        return results

@mcp.tool()
async def search_unii_by_name(name: str) -> Dict[str, Any]:
    """
    根据物质名称搜索 UNII 编号
    
    Args:
        name: 物质名称（中文或英文）
    
    Returns:
        包含 UNII 搜索结果的字典
    """
    try:
        async with UNIISearcher() as searcher:
            # 生成搜索词
            search_terms = searcher.generate_search_terms(name)
            logger.info(f"生成的搜索词: {search_terms}")
            
            all_results = []
            
            # 对每个搜索词进行搜索
            for term in search_terms:
                result = await searcher.search_unii(term)
                unii_info = searcher.extract_unii_info(result)
                all_results.extend(unii_info)
            
            # 去重（基于 UNII 编号）
            unique_results = {}
            for result in all_results:
                unii = result.get("unii")
                if unii and unii not in unique_results:
                    unique_results[unii] = result
            
            final_results = list(unique_results.values())
            
            return {
                "input_name": name,
                "search_terms": search_terms,
                "total_found": len(final_results),
                "results": final_results,
                "success": True
            }
    
    except Exception as e:
        logger.error(f"搜索 UNII 时发生错误: {e}")
        return {
            "input_name": name,
            "error": str(e),
            "success": False
        }

@mcp.tool()
async def get_unii_details(unii: str) -> Dict[str, Any]:
    """
    根据 UNII 编号获取详细信息
    
    Args:
        unii: UNII 编号
    
    Returns:
        包含详细信息的字典
    """
    try:
        async with UNIISearcher() as searcher:
            # 构建详细信息 URL
            detail_url = f"{UNII_SEARCH_BASE_URL}{unii}?format=json"
            
            async with searcher.session.get(detail_url) as response:
                if response.status == 200:
                    data = await response.json()
                    return {
                        "unii": unii,
                        "success": True,
                        "data": data,
                        "url": detail_url
                    }
                else:
                    return {
                        "unii": unii,
                        "success": False,
                        "error": f"HTTP {response.status}",
                        "url": detail_url
                    }
    
    except Exception as e:
        logger.error(f"获取 UNII 详细信息时发生错误: {e}")
        return {
            "unii": unii,
            "success": False,
            "error": str(e)
        }

if __name__ == "__main__":
    # 运行 MCP 服务器
    mcp.run()
