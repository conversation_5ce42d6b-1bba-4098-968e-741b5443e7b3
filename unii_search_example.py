#!/usr/bin/env python3
"""
UNII Search MCP 使用示例
演示如何使用 UNII 搜索工具
"""

import asyncio
import json
from unii_search_mcp import UNIISearcher

async def test_unii_search():
    """测试 UNII 搜索功能"""
    
    # 测试用例
    test_cases = [
        "阿司匹林",
        "aspirin", 
        "胰岛素",
        "insulin",
        "聚维酮",
        "povidone",
        "苯甲醇",
        "benzyl alcohol",
        "维生素C",
        "ascorbic acid"
    ]
    
    print("=== UNII 搜索测试 ===\n")
    
    async with UNIISearcher() as searcher:
        for test_term in test_cases:
            print(f"🔍 搜索: {test_term}")
            print("-" * 50)
            
            # 生成搜索词
            search_terms = searcher.generate_search_terms(test_term)
            print(f"生成的搜索词: {search_terms}")
            
            # 搜索 UNII
            all_results = []
            for term in search_terms:
                result = await searcher.search_unii(term)
                if result.get("success"):
                    unii_info = searcher.extract_unii_info(result)
                    all_results.extend(unii_info)
                    print(f"  ✅ {term}: 找到 {len(unii_info)} 个结果")
                else:
                    print(f"  ❌ {term}: {result.get('error', '搜索失败')}")
            
            # 去重并显示结果
            unique_results = {}
            for result in all_results:
                unii = result.get("unii")
                if unii and unii not in unique_results:
                    unique_results[unii] = result
            
            final_results = list(unique_results.values())
            
            if final_results:
                print(f"\n📋 最终结果 ({len(final_results)} 个):")
                for i, result in enumerate(final_results, 1):
                    print(f"  {i}. UNII: {result.get('unii', 'N/A')}")
                    print(f"     名称: {result.get('preferred_name', 'N/A')}")
                    print(f"     显示名: {result.get('display_name', 'N/A')}")
                    print(f"     分子式: {result.get('molecular_formula', 'N/A')}")
                    print(f"     CAS号: {result.get('cas_number', 'N/A')}")
                    print()
            else:
                print("  ❌ 未找到任何 UNII 结果")
            
            print("=" * 60)
            print()

async def test_specific_unii():
    """测试特定 UNII 编号的详细信息获取"""
    
    print("=== 特定 UNII 详细信息测试 ===\n")
    
    # 已知的一些 UNII 编号用于测试
    test_uniis = [
        "R16CO5Y76E",  # aspirin
        "3PJY347X20",  # insulin
        "FZ989GH94E",  # povidone
    ]
    
    async with UNIISearcher() as searcher:
        for unii in test_uniis:
            print(f"🔍 获取 UNII 详细信息: {unii}")
            print("-" * 50)
            
            # 构建详细信息 URL
            detail_url = f"https://precision.fda.gov/uniisearch/srs/unii/{unii}?format=json"
            
            try:
                async with searcher.session.get(detail_url) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"  ✅ 成功获取详细信息")
                        print(f"  📄 数据: {json.dumps(data, indent=2, ensure_ascii=False)[:500]}...")
                    else:
                        print(f"  ❌ HTTP {response.status}")
            except Exception as e:
                print(f"  ❌ 错误: {e}")
            
            print()

def main():
    """主函数"""
    print("UNII Search MCP 测试程序")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_unii_search())
    asyncio.run(test_specific_unii())

if __name__ == "__main__":
    main()
