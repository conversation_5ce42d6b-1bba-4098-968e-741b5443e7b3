# UNII Search MCP Server

基于 fastmcp 的 UNII（Unique Ingredient Identifier）编号检索工具，支持中英文输入，自动生成精准的英文检索词，并通过 FDA 的 UNII Search API 获取物质的 UNII 编号。

## 🚀 功能特性

- 🔍 **智能检索词生成**: 输入中文名词，自动生成最精准的英文检索词
- 🌐 **FDA 官方 API**: 直接调用 https://precision.fda.gov/uniisearch 获取权威数据
- 🔄 **多词搜索**: 支持多个变体词同时搜索，提高命中率
- 📊 **结构化结果**: 返回 UNII 编号、分子式、CAS 号等详细信息
- ⚡ **异步处理**: 基于 aiohttp 的高性能异步搜索
- 🛠️ **MCP 协议**: 标准的 Model Context Protocol 接口

## 📦 安装依赖

```bash
pip install -r requirements_unii.txt
```

或手动安装：

```bash
pip install fastmcp aiohttp
```

## 🎯 使用方法

### 1. 作为 MCP 服务器运行

```bash
python unii_search_mcp.py
```

### 2. 在代码中使用

```python
from unii_search_mcp import search_unii_by_name, get_unii_details

# 搜索 UNII 编号
result = await search_unii_by_name("阿司匹林")
print(result)

# 获取特定 UNII 的详细信息
details = await get_unii_details("R16CO5Y76E")
print(details)
```

### 3. 运行测试示例

```bash
python unii_search_example.py
```

## 🔧 API 接口

### `search_unii_by_name(name: str)`

根据物质名称搜索 UNII 编号。

**参数:**
- `name`: 物质名称（支持中文或英文）

**返回:**
```json
{
  "input_name": "阿司匹林",
  "search_terms": ["aspirin", "Aspirin", "ASPIRIN"],
  "total_found": 1,
  "results": [
    {
      "unii": "R16CO5Y76E",
      "preferred_name": "ASPIRIN",
      "display_name": "Aspirin",
      "substance_class": "Chemical",
      "molecular_formula": "C9H8O4",
      "molecular_weight": "180.16",
      "cas_number": "50-78-2",
      "search_term": "aspirin"
    }
  ],
  "success": true
}
```

### `get_unii_details(unii: str)`

根据 UNII 编号获取详细信息。

**参数:**
- `unii`: UNII 编号

**返回:**
```json
{
  "unii": "R16CO5Y76E",
  "success": true,
  "data": { /* 详细的 FDA 数据 */ },
  "url": "https://precision.fda.gov/uniisearch/srs/unii/R16CO5Y76E?format=json"
}
```

## 📚 支持的物质类型

工具内置了常见药物成分的中英文对照词典：

### 常见药物
- 阿司匹林 → aspirin
- 对乙酰氨基酚 → acetaminophen  
- 布洛芬 → ibuprofen
- 青霉素 → penicillin
- 胰岛素 → insulin
- 肝素 → heparin

### 辅料成分
- 聚维酮 → povidone
- 聚乙二醇 → polyethylene glycol
- 苯甲醇 → benzyl alcohol
- 丙二醇 → propylene glycol
- 甘油 → glycerin

### 化学物质
- 氯化钠 → sodium chloride
- 碳酸钙 → calcium carbonate
- 硫酸镁 → magnesium sulfate
- 柠檬酸 → citric acid

## 🔍 搜索策略

1. **词典匹配**: 首先查找内置的中英文对照词典
2. **变体生成**: 生成大小写变体（小写、大写、标题格式）
3. **多词搜索**: 对所有变体词并行搜索
4. **结果去重**: 基于 UNII 编号去除重复结果
5. **结果排序**: 按相关性和完整性排序

## 🌐 FDA UNII Search API

本工具使用 FDA 官方的 UNII Search API：
- **基础 URL**: https://precision.fda.gov/uniisearch/srs/unii/
- **搜索格式**: `?query={term}&format=json`
- **详情格式**: `/{unii}?format=json`

## ⚠️ 注意事项

1. **网络连接**: 需要稳定的网络连接访问 FDA API
2. **请求频率**: 工具会自动控制请求频率，避免对服务器造成压力
3. **数据准确性**: 搜索结果基于 FDA 官方数据，但建议人工复核重要结果
4. **API 限制**: 遵守 FDA API 的使用条款和限制

## 🐛 故障排除

### 常见问题

1. **连接超时**: 检查网络连接，确保可以访问 precision.fda.gov
2. **搜索无结果**: 尝试使用英文名称或化学名称搜索
3. **中文翻译不准确**: 可以直接使用英文名称搜索

### 调试模式

修改日志级别查看详细信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📄 许可证

本工具仅供学习和研究使用。使用时请遵守相关法律法规和 FDA API 的使用条款。

## 🔄 更新日志

- v1.0: 初始版本，支持基本的 UNII 搜索功能
- 支持中英文输入和智能检索词生成
- 集成 FDA 官方 UNII Search API
- 提供完整的 MCP 服务器接口
