# DailyMed CLI 用户手册

## 📖 概述

DailyMed CLI是一个功能强大的命令行工具，专门用于搜索和分析DailyMed数据库中的药品信息。该工具特别专注于注射剂的识别和过滤，提供高精度的搜索结果和详细的分析报告。

## 🚀 快速开始

### 安装和设置

1. 确保Python 3.6+已安装
2. 将所有文件放在同一目录中
3. 使脚本可执行：`chmod +x dailymed`

### 基本用法

```bash
# 显示帮助信息
./dailymed --help

# 搜索含酒精的注射剂
./dailymed search --ingredient ALCOHOL --filter-injections

# 生成HTML报告
./dailymed report --input results.csv --format html
```

## 📋 命令详解

### 1. search - 搜索药品

搜索DailyMed数据库中的药品信息。

#### 基本语法
```bash
./dailymed search --ingredient <成分名称> [选项]
```

#### 必需参数
- `--ingredient, -i`: 搜索的成分名称（必需）

#### 搜索参数
- `--type, -t`: 搜索类型
  - `active`: 活性成分
  - `inactive`: 非活性成分（默认）
  - `drug_name`: 药品名称
  - `manufacturer`: 制造商
- `--label-type`: 标签类型
  - `human`: 人用药品（默认）
  - `animal`: 动物用药品

#### 过滤选项
- `--filter-injections`: 只返回注射剂
- `--verify-terms`: 验证搜索词在药品详情中存在（默认启用）
- `--no-verify-terms`: 禁用搜索词验证
- `--confidence`: 置信度过滤
  - `high`: 只返回高置信度结果
  - `medium`: 返回高和中置信度结果
  - `all`: 返回所有结果（默认）

#### 限制选项
- `--max-drugs`: 最大检查药品数（默认：100）
- `--max-pages`: 最大搜索页数
- `--delay`: 请求间隔时间，秒（默认：0.5）

#### 输出选项
- `--output, -o`: 输出文件路径（默认：自动生成）
- `--format`: 输出格式
  - `csv`: CSV格式（默认）
  - `json`: JSON格式
  - `xlsx`: Excel格式
- `--include-dosage`: 包含DOSAGE AND ADMINISTRATION信息（默认启用）
- `--include-full-content`: 包含完整药品内容

#### 示例

```bash
# 基础搜索：含酒精的注射剂
./dailymed search --ingredient ALCOHOL --filter-injections

# 高级搜索：含苯甲醇的高置信度注射剂
./dailymed search --ingredient "BENZYL ALCOHOL" --filter-injections \
                  --confidence high --max-drugs 50

# 搜索活性成分：胰岛素注射剂
./dailymed search --ingredient INSULIN --type active \
                  --filter-injections --output insulin_results.json --format json

# 搜索制造商的所有药品
./dailymed search --ingredient PFIZER --type manufacturer \
                  --max-drugs 200 --no-verify-terms
```

### 2. batch - 批量搜索

执行多个预定义的搜索任务。

#### 基本语法
```bash
./dailymed batch --config-file <配置文件> [选项]
```

#### 参数
- `--config-file, -c`: 批量搜索配置文件（JSON格式，必需）
- `--output-dir, -o`: 输出目录（默认：./batch_results）
- `--parallel`: 并行搜索数量（默认：1）
- `--resume`: 从上次中断处继续

#### 配置文件格式

```json
{
  "description": "DailyMed批量搜索配置",
  "global_settings": {
    "max_drugs": 100,
    "delay": 0.5,
    "filter_injections": true,
    "verify_terms": true,
    "confidence_filter": "all",
    "output_format": "csv"
  },
  "searches": [
    {
      "name": "含酒精的注射剂",
      "ingredient": "ALCOHOL",
      "type": "inactive",
      "max_drugs": 100
    },
    {
      "name": "含苯甲醇的注射剂",
      "ingredient": "BENZYL ALCOHOL",
      "type": "inactive",
      "max_drugs": 50
    }
  ]
}
```

#### 示例

```bash
# 执行批量搜索
./dailymed batch --config-file batch_config.json --output-dir results/

# 生成批量配置模板
python3 -c "
from dailymed_utils import create_batch_config_template
import json
config = create_batch_config_template()
with open('batch_template.json', 'w') as f:
    json.dump(config, f, indent=2)
print('批量配置模板已生成: batch_template.json')
"
```

### 3. report - 生成报告

从搜索结果生成详细的分析报告。

#### 基本语法
```bash
./dailymed report --input <输入文件> [选项]
```

#### 参数
- `--input, -i`: 输入CSV文件路径（必需）
- `--output, -o`: 输出报告文件路径（默认：自动生成）
- `--format`: 报告格式
  - `html`: HTML格式（默认）
  - `markdown`: Markdown格式
  - `pdf`: PDF格式（开发中）
- `--template`: 自定义报告模板
- `--include-charts`: 包含图表（默认启用）

#### 示例

```bash
# 生成HTML报告
./dailymed report --input results.csv --format html

# 生成Markdown报告
./dailymed report --input results.csv --format markdown \
                  --output analysis_report.md

# 生成带图表的详细HTML报告
./dailymed report --input results.csv --format html \
                  --include-charts --output detailed_report.html
```

### 4. config - 配置管理

管理CLI工具的配置设置。

#### 基本语法
```bash
./dailymed config [选项]
```

#### 参数
- `--show`: 显示当前配置
- `--set KEY=VALUE`: 设置配置项（可多次使用）
- `--reset`: 重置为默认配置

#### 示例

```bash
# 显示当前配置
./dailymed config --show

# 设置请求延迟和超时
./dailymed config --set delay=1.0 --set timeout=60

# 重置配置
./dailymed config --reset
```

### 5. validate - 验证数据

验证搜索结果数据的完整性和准确性。

#### 基本语法
```bash
./dailymed validate --input <输入文件> [选项]
```

#### 参数
- `--input, -i`: 要验证的CSV文件路径（必需）
- `--check-urls`: 检查药品URL的有效性
- `--revalidate-terms`: 重新验证搜索词
- `--output, -o`: 验证报告输出路径

#### 示例

```bash
# 验证URL有效性
./dailymed validate --input results.csv --check-urls

# 完整验证
./dailymed validate --input results.csv --check-urls \
                    --revalidate-terms --output validation_report.json
```

## 🔧 全局选项

所有命令都支持以下全局选项：

- `--version`: 显示版本信息
- `-v, --verbose`: 增加输出详细程度（可重复使用：-v, -vv, -vvv）
- `--config`: 配置文件路径（默认：~/.dailymed_cli.json）

## 📊 输出格式

### CSV格式
标准的逗号分隔值格式，包含以下字段：
- `name`: 药品名称
- `setid`: 药品唯一标识符
- `url`: 药品详情页面URL
- `title`: 完整标题
- `dosage_administration`: DOSAGE AND ADMINISTRATION内容
- `is_injection`: 是否为注射剂
- `confidence`: 置信度（high/medium/low）
- `dosage_has_injection_keywords`: DOSAGE部分是否包含注射剂关键词
- `injection_keywords`: 找到的注射剂关键词
- `search_term_verified`: 搜索词是否验证通过
- `found_search_terms`: 在详情中找到的搜索词

### JSON格式
结构化的JSON格式，包含元数据和搜索结果：
```json
{
  "metadata": {
    "generated_at": "2025-09-02T14:30:00",
    "total_results": 25,
    "statistics": {...}
  },
  "results": [...]
}
```

### Excel格式
包含两个工作表：
- `搜索结果`: 主要数据
- `统计信息`: 搜索统计

## 🎯 高级用法

### 1. 精确搜索策略

```bash
# 高精度注射剂搜索
./dailymed search --ingredient "BENZYL ALCOHOL" --filter-injections \
                  --confidence high --verify-terms --max-drugs 200

# 快速概览搜索
./dailymed search --ingredient ALCOHOL --filter-injections \
                  --confidence medium --max-drugs 50 --delay 0.3
```

### 2. 数据管道

```bash
# 搜索 -> 验证 -> 报告
./dailymed search --ingredient ALCOHOL --filter-injections -o step1.csv
./dailymed validate --input step1.csv --check-urls -o validation.json
./dailymed report --input step1.csv --format html -o final_report.html
```

### 3. 批量分析

```bash
# 创建批量配置
cat > comprehensive_search.json << EOF
{
  "global_settings": {
    "filter_injections": true,
    "confidence_filter": "high",
    "max_drugs": 150
  },
  "searches": [
    {"name": "酒精注射剂", "ingredient": "ALCOHOL", "type": "inactive"},
    {"name": "苯甲醇注射剂", "ingredient": "BENZYL ALCOHOL", "type": "inactive"},
    {"name": "丙二醇注射剂", "ingredient": "PROPYLENE GLYCOL", "type": "inactive"},
    {"name": "胰岛素制剂", "ingredient": "INSULIN", "type": "active"}
  ]
}
EOF

# 执行批量搜索
./dailymed batch -c comprehensive_search.json -o comprehensive_results/

# 为每个结果生成报告
for file in comprehensive_results/*.csv; do
    ./dailymed report --input "$file" --format html
done
```

## ⚠️ 注意事项

1. **请求频率**: 默认延迟0.5秒，避免对DailyMed服务器造成过大压力
2. **网络连接**: 需要稳定的网络连接访问DailyMed网站
3. **数据准确性**: 注射剂识别基于关键词匹配，建议人工复核重要结果
4. **法律合规**: 请遵守DailyMed的使用条款和相关法律法规

## 🐛 故障排除

### 常见问题

1. **连接超时**
   ```bash
   # 增加延迟和超时时间
   ./dailymed config --set delay=1.0 --set timeout=60
   ```

2. **没有找到注射剂**
   ```bash
   # 降低置信度要求
   ./dailymed search --ingredient ALCOHOL --confidence medium
   ```

3. **搜索结果为空**
   ```bash
   # 禁用搜索词验证
   ./dailymed search --ingredient ALCOHOL --no-verify-terms
   ```

### 调试模式

```bash
# 启用详细日志
./dailymed -vv search --ingredient ALCOHOL --max-drugs 10

# 启用最详细日志
./dailymed -vvv search --ingredient ALCOHOL --max-drugs 5
```

## 📞 支持

如需帮助或报告问题，请：
1. 查看详细日志输出（使用 -vv 或 -vvv）
2. 检查网络连接和DailyMed网站状态
3. 验证搜索参数的正确性

---

*DailyMed CLI v1.0.0 - 专业的药品数据搜索和分析工具*
