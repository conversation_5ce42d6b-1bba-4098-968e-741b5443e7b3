#!/usr/bin/env python3
"""
DailyMed CLI - 可执行入口脚本

这是DailyMed CLI工具的主要入口点。
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 导入并运行主程序
try:
    from dailymed_cli import main
    main()
except ImportError as e:
    print(f"错误: 无法导入必要的模块: {e}")
    print("请确保所有依赖文件都在当前目录中")
    sys.exit(1)
except Exception as e:
    print(f"执行错误: {e}")
    sys.exit(1)
