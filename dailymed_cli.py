#!/usr/bin/env python3
"""
DailyMed CLI - 专业的药品数据搜索和分析工具

一个功能强大的命令行工具，用于搜索DailyMed数据库中的药品信息，
特别专注于注射剂的识别和过滤。

作者: DailyMed CLI Team
版本: 1.0.0
"""

import argparse
import sys
import os
import json
import logging
import csv
import urllib.request
from datetime import datetime
from pathlib import Path

# 导入核心模块
from dailymed_core import DailyMedSearchEngine
from dailymed_utils import setup_logging, load_config, save_config
from dailymed_reports import ReportGenerator

__version__ = "1.0.0"

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        prog='dailymed-cli',
        description='DailyMed药品数据搜索和分析工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 基础搜索
  dailymed-cli search --ingredient ALCOHOL --type inactive --max-drugs 100
  
  # 高级搜索
  dailymed-cli search --ingredient "BENZYL ALCOHOL" --type inactive \\
                     --filter-injections --verify-terms --output results.csv
  
  # 批量搜索
  dailymed-cli batch --config batch_config.json --output-dir results/
  
  # 生成报告
  dailymed-cli report --input results.csv --format html --output report.html
  
  # 配置管理
  dailymed-cli config --set delay=1.0 --set max_retries=3

更多信息请访问: https://github.com/dailymed-cli
        """
    )
    
    parser.add_argument('--version', action='version', version=f'%(prog)s {__version__}')
    parser.add_argument('-v', '--verbose', action='count', default=0,
                       help='增加输出详细程度 (-v, -vv, -vvv)')
    parser.add_argument('--config', type=str, default='~/.dailymed_cli.json',
                       help='配置文件路径 (默认: ~/.dailymed_cli.json)')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索药品')
    add_search_arguments(search_parser)
    
    # 批量搜索命令
    batch_parser = subparsers.add_parser('batch', help='批量搜索')
    add_batch_arguments(batch_parser)
    
    # 报告生成命令
    report_parser = subparsers.add_parser('report', help='生成报告')
    add_report_arguments(report_parser)
    
    # 配置管理命令
    config_parser = subparsers.add_parser('config', help='配置管理')
    add_config_arguments(config_parser)
    
    # 验证命令
    validate_parser = subparsers.add_parser('validate', help='验证数据')
    add_validate_arguments(validate_parser)
    
    return parser

def add_search_arguments(parser):
    """添加搜索命令的参数"""
    # 基础搜索参数
    search_group = parser.add_argument_group('搜索参数')
    search_group.add_argument('--ingredient', '-i', required=True,
                             help='搜索的成分名称 (必需)')
    search_group.add_argument('--type', '-t', choices=['active', 'inactive', 'drug_name', 'manufacturer'],
                             default='inactive', help='搜索类型 (默认: inactive)')
    search_group.add_argument('--label-type', choices=['human', 'animal'],
                             default='human', help='标签类型 (默认: human)')
    
    # 过滤参数
    filter_group = parser.add_argument_group('过滤选项')
    filter_group.add_argument('--filter-injections', action='store_true',
                             help='只返回注射剂')
    filter_group.add_argument('--verify-terms', action='store_true', default=True,
                             help='验证搜索词在药品详情中存在 (默认启用)')
    filter_group.add_argument('--no-verify-terms', dest='verify_terms', action='store_false',
                             help='禁用搜索词验证')
    filter_group.add_argument('--confidence', choices=['high', 'medium', 'all'],
                             default='all', help='置信度过滤 (默认: all)')
    
    # 限制参数
    limit_group = parser.add_argument_group('限制选项')
    limit_group.add_argument('--max-drugs', type=int, default=100,
                            help='最大检查药品数 (默认: 100)')
    limit_group.add_argument('--max-pages', type=int,
                            help='最大搜索页数')
    limit_group.add_argument('--delay', type=float, default=0.5,
                            help='请求间隔时间(秒) (默认: 0.5)')
    
    # 输出参数
    output_group = parser.add_argument_group('输出选项')
    output_group.add_argument('--output', '-o', 
                             help='输出文件路径 (默认: 自动生成)')
    output_group.add_argument('--format', choices=['csv', 'json', 'xlsx'],
                             default='csv', help='输出格式 (默认: csv)')
    output_group.add_argument('--include-dosage', action='store_true', default=True,
                             help='包含DOSAGE AND ADMINISTRATION信息 (默认启用)')
    output_group.add_argument('--include-full-content', action='store_true',
                             help='包含完整药品内容')

def add_batch_arguments(parser):
    """添加批量搜索命令的参数"""
    parser.add_argument('--config-file', '-c', required=True,
                       help='批量搜索配置文件 (JSON格式)')
    parser.add_argument('--output-dir', '-o', default='./batch_results',
                       help='输出目录 (默认: ./batch_results)')
    parser.add_argument('--parallel', type=int, default=1,
                       help='并行搜索数量 (默认: 1)')
    parser.add_argument('--resume', action='store_true',
                       help='从上次中断处继续')

def add_report_arguments(parser):
    """添加报告生成命令的参数"""
    parser.add_argument('--input', '-i', required=True,
                       help='输入CSV文件路径')
    parser.add_argument('--output', '-o', 
                       help='输出报告文件路径')
    parser.add_argument('--format', choices=['html', 'pdf', 'markdown'],
                       default='html', help='报告格式 (默认: html)')
    parser.add_argument('--template', 
                       help='自定义报告模板')
    parser.add_argument('--include-charts', action='store_true', default=True,
                       help='包含图表 (默认启用)')

def add_config_arguments(parser):
    """添加配置管理命令的参数"""
    config_group = parser.add_mutually_exclusive_group(required=True)
    config_group.add_argument('--show', action='store_true',
                             help='显示当前配置')
    config_group.add_argument('--set', action='append', metavar='KEY=VALUE',
                             help='设置配置项 (可多次使用)')
    config_group.add_argument('--reset', action='store_true',
                             help='重置为默认配置')

def add_validate_arguments(parser):
    """添加验证命令的参数"""
    parser.add_argument('--input', '-i', required=True,
                       help='要验证的CSV文件路径')
    parser.add_argument('--check-urls', action='store_true',
                       help='检查药品URL的有效性')
    parser.add_argument('--revalidate-terms', action='store_true',
                       help='重新验证搜索词')
    parser.add_argument('--output', '-o',
                       help='验证报告输出路径')

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    # 如果没有提供命令，显示帮助
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # 设置日志
    log_level = logging.WARNING
    if args.verbose == 1:
        log_level = logging.INFO
    elif args.verbose >= 2:
        log_level = logging.DEBUG
    
    setup_logging(log_level)
    logger = logging.getLogger(__name__)
    
    try:
        # 加载配置
        config_path = Path(args.config).expanduser()
        config = load_config(config_path)
        
        # 执行相应的命令
        if args.command == 'search':
            exit_code = handle_search_command(args, config)
        elif args.command == 'batch':
            exit_code = handle_batch_command(args, config)
        elif args.command == 'report':
            exit_code = handle_report_command(args, config)
        elif args.command == 'config':
            exit_code = handle_config_command(args, config, config_path)
        elif args.command == 'validate':
            exit_code = handle_validate_command(args, config)
        else:
            logger.error(f"未知命令: {args.command}")
            exit_code = 1
        
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(130)
    except Exception as e:
        logger.error(f"执行失败: {e}")
        if args.verbose >= 2:
            import traceback
            traceback.print_exc()
        sys.exit(1)

def handle_search_command(args, config):
    """处理搜索命令"""
    logger = logging.getLogger(__name__)
    logger.info(f"开始搜索: {args.type.upper()}:({args.ingredient})")
    
    # 创建搜索引擎
    engine = DailyMedSearchEngine(config)
    
    # 生成输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_ingredient = args.ingredient.lower().replace(' ', '_').replace('(', '').replace(')', '')
        args.output = f"dailymed_{safe_ingredient}_{timestamp}.{args.format}"
    
    # 执行搜索
    try:
        results = engine.search(
            ingredient=args.ingredient,
            search_type=args.type,
            label_type=args.label_type,
            filter_injections=args.filter_injections,
            verify_terms=args.verify_terms,
            confidence_filter=args.confidence,
            max_drugs=args.max_drugs,
            max_pages=args.max_pages,
            delay=args.delay,
            include_dosage=args.include_dosage,
            include_full_content=args.include_full_content
        )
        
        # 保存结果
        engine.save_results(results, args.output, args.format)
        
        # 显示摘要
        total_found = len(results)
        high_confidence = sum(1 for r in results if r.get('confidence') == 'high')
        
        logger.info(f"搜索完成!")
        logger.info(f"找到 {total_found} 个结果")
        if args.filter_injections:
            logger.info(f"其中高置信度注射剂: {high_confidence} 个")
        logger.info(f"结果已保存到: {args.output}")
        
        return 0
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return 1

def handle_batch_command(args, config):
    """处理批量搜索命令"""
    logger = logging.getLogger(__name__)
    logger.info("开始批量搜索")

    try:
        # 加载批量配置
        from dailymed_utils import load_batch_config, ensure_directory, ProgressTracker
        batch_config = load_batch_config(args.config_file)

        # 创建输出目录
        output_dir = Path(args.output_dir)
        ensure_directory(output_dir)

        # 获取全局设置
        global_settings = batch_config.get('global_settings', {})
        searches = batch_config.get('searches', [])

        logger.info(f"将执行 {len(searches)} 个搜索任务")

        # 创建搜索引擎
        engine = DailyMedSearchEngine(config)

        # 执行批量搜索
        progress = ProgressTracker(len(searches), "批量搜索")
        results_summary = []

        for i, search_config in enumerate(searches):
            search_name = search_config.get('name', f'搜索_{i+1}')
            logger.info(f"执行搜索: {search_name}")

            # 合并配置
            search_params = {**global_settings, **search_config}

            try:
                # 执行搜索
                results = engine.search(
                    ingredient=search_params['ingredient'],
                    search_type=search_params.get('type', 'inactive'),
                    filter_injections=search_params.get('filter_injections', True),
                    verify_terms=search_params.get('verify_terms', True),
                    max_drugs=search_params.get('max_drugs', 100),
                    delay=search_params.get('delay', 0.5)
                )

                # 保存结果
                safe_name = search_params['ingredient'].replace(' ', '_').replace('(', '').replace(')', '')
                output_file = output_dir / f"{safe_name}_results.{search_params.get('output_format', 'csv')}"
                engine.save_results(results, str(output_file), search_params.get('output_format', 'csv'))

                # 记录摘要
                stats = engine.get_statistics()
                results_summary.append({
                    'name': search_name,
                    'ingredient': search_params['ingredient'],
                    'total_found': len(results),
                    'output_file': str(output_file),
                    'statistics': stats
                })

                logger.info(f"搜索完成: {search_name}, 找到 {len(results)} 个结果")

            except Exception as e:
                logger.error(f"搜索失败: {search_name}, 错误: {e}")
                results_summary.append({
                    'name': search_name,
                    'ingredient': search_params['ingredient'],
                    'error': str(e)
                })

            progress.update()

        progress.finish()

        # 生成批量搜索报告
        summary_file = output_dir / 'batch_summary.json'
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                'batch_config': batch_config,
                'results': results_summary,
                'generated_at': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"批量搜索完成，摘要已保存到: {summary_file}")
        return 0

    except Exception as e:
        logger.error(f"批量搜索失败: {e}")
        return 1

def handle_report_command(args, config):
    """处理报告生成命令"""
    logger = logging.getLogger(__name__)
    logger.info("开始生成报告")

    try:
        # 创建报告生成器
        report_gen = ReportGenerator()

        # 加载数据
        report_gen.load_data(args.input)

        # 生成输出文件名
        if not args.output:
            input_path = Path(args.input)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            args.output = f"report_{input_path.stem}_{timestamp}.{args.format}"

        # 生成报告
        if args.format == 'html':
            report_gen.generate_html_report(args.output, args.include_charts)
        elif args.format == 'markdown':
            report_gen.generate_markdown_report(args.output)
        elif args.format == 'pdf':
            logger.error("PDF格式暂不支持")
            return 1

        # 显示摘要
        summary = report_gen.generate_summary()
        logger.info("报告摘要:")
        for line in summary.split('\n'):
            if line.strip():
                logger.info(f"  {line.strip()}")

        logger.info(f"报告已生成: {args.output}")
        return 0

    except Exception as e:
        logger.error(f"报告生成失败: {e}")
        return 1

def handle_config_command(args, config, config_path):
    """处理配置管理命令"""
    logger = logging.getLogger(__name__)
    
    if args.show:
        print("当前配置:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        return 0
    elif args.set:
        for item in args.set:
            if '=' not in item:
                logger.error(f"无效的配置项格式: {item}")
                return 1
            key, value = item.split('=', 1)
            # 尝试转换数值
            try:
                if '.' in value:
                    value = float(value)
                else:
                    value = int(value)
            except ValueError:
                if value.lower() in ('true', 'false'):
                    value = value.lower() == 'true'
            config[key] = value
            logger.info(f"设置 {key} = {value}")
        
        save_config(config, config_path)
        logger.info(f"配置已保存到: {config_path}")
        return 0
    elif args.reset:
        default_config = {
            "delay": 0.5,
            "max_retries": 3,
            "timeout": 30,
            "user_agent": "DailyMed-CLI/1.0.0"
        }
        save_config(default_config, config_path)
        logger.info("配置已重置为默认值")
        return 0

def handle_validate_command(args, config):
    """处理验证命令"""
    logger = logging.getLogger(__name__)
    logger.info("开始验证数据")

    try:
        # 加载数据
        input_path = Path(args.input)
        if not input_path.exists():
            logger.error(f"输入文件不存在: {args.input}")
            return 1

        # 读取CSV数据
        data = []
        with open(input_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            data = list(reader)

        logger.info(f"加载了 {len(data)} 条记录")

        validation_results = {
            'total_records': len(data),
            'valid_urls': 0,
            'invalid_urls': 0,
            'revalidated_terms': 0,
            'validation_errors': []
        }

        # 创建搜索引擎用于重新验证
        engine = DailyMedSearchEngine(config) if args.revalidate_terms else None

        from dailymed_utils import ProgressTracker
        progress = ProgressTracker(len(data), "验证数据")

        for i, record in enumerate(data):
            try:
                # 检查URL有效性
                if args.check_urls:
                    url = record.get('url', '')
                    if url:
                        try:
                            req = urllib.request.Request(url, headers={'User-Agent': 'DailyMed-CLI/1.0.0'})
                            with urllib.request.urlopen(req, timeout=10) as response:
                                if response.status == 200:
                                    validation_results['valid_urls'] += 1
                                else:
                                    validation_results['invalid_urls'] += 1
                                    validation_results['validation_errors'].append({
                                        'record': i+1,
                                        'error': f'URL返回状态码: {response.status}',
                                        'url': url
                                    })
                        except Exception as e:
                            validation_results['invalid_urls'] += 1
                            validation_results['validation_errors'].append({
                                'record': i+1,
                                'error': f'URL访问失败: {str(e)}',
                                'url': url
                            })

                # 重新验证搜索词
                if args.revalidate_terms and engine:
                    setid = record.get('setid', '')
                    if setid:
                        # 这里可以实现重新验证逻辑
                        validation_results['revalidated_terms'] += 1

                if i % 10 == 0:
                    progress.update(10)

            except Exception as e:
                validation_results['validation_errors'].append({
                    'record': i+1,
                    'error': f'验证记录失败: {str(e)}'
                })

        progress.finish()

        # 生成验证报告
        logger.info("验证完成:")
        logger.info(f"  总记录数: {validation_results['total_records']}")
        if args.check_urls:
            logger.info(f"  有效URL: {validation_results['valid_urls']}")
            logger.info(f"  无效URL: {validation_results['invalid_urls']}")
        if args.revalidate_terms:
            logger.info(f"  重新验证的搜索词: {validation_results['revalidated_terms']}")

        if validation_results['validation_errors']:
            logger.warning(f"发现 {len(validation_results['validation_errors'])} 个验证错误")

        # 保存验证报告
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(validation_results, f, indent=2, ensure_ascii=False)
            logger.info(f"验证报告已保存到: {args.output}")

        return 0

    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return 1

if __name__ == '__main__':
    main()
