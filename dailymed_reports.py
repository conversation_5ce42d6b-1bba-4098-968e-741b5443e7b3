#!/usr/bin/env python3
"""
DailyMed报告生成模块

提供多种格式的报告生成功能，包括HTML、PDF、Markdown等
"""

import csv
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

class ReportGenerator:
    """报告生成器"""
    
    def __init__(self):
        self.data = []
        self.metadata = {}
    
    def load_data(self, input_file: str):
        """加载数据文件"""
        input_path = Path(input_file)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入文件不存在: {input_file}")
        
        if input_path.suffix.lower() == '.csv':
            self._load_csv(input_path)
        elif input_path.suffix.lower() == '.json':
            self._load_json(input_path)
        else:
            raise ValueError(f"不支持的文件格式: {input_path.suffix}")
        
        logger.info(f"已加载 {len(self.data)} 条记录")
    
    def _load_csv(self, file_path: Path):
        """加载CSV文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            self.data = list(reader)
    
    def _load_json(self, file_path: Path):
        """加载JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
            if isinstance(json_data, dict) and 'results' in json_data:
                self.data = json_data['results']
                self.metadata = json_data.get('metadata', {})
            elif isinstance(json_data, list):
                self.data = json_data
            else:
                raise ValueError("无效的JSON格式")
    
    def generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        if not self.data:
            return {}
        
        stats = {
            'total_drugs': len(self.data),
            'injection_drugs': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'low_confidence': 0,
            'verified_terms': 0,
            'dosage_with_injection_keywords': 0,
            'top_keywords': {},
            'confidence_distribution': {},
            'search_term_verification_rate': 0
        }
        
        keyword_counts = {}
        
        for drug in self.data:
            # 注射剂统计
            if drug.get('is_injection') == 'True' or drug.get('is_injection') is True:
                stats['injection_drugs'] += 1
            
            # 置信度统计
            confidence = drug.get('confidence', 'low')
            if confidence == 'high':
                stats['high_confidence'] += 1
            elif confidence == 'medium':
                stats['medium_confidence'] += 1
            else:
                stats['low_confidence'] += 1
            
            # 搜索词验证统计
            if drug.get('search_term_verified') == 'True' or drug.get('search_term_verified') is True:
                stats['verified_terms'] += 1
            
            # DOSAGE部分关键词统计
            if drug.get('dosage_has_injection_keywords') == 'True' or drug.get('dosage_has_injection_keywords') is True:
                stats['dosage_with_injection_keywords'] += 1
            
            # 关键词频率统计
            keywords = drug.get('injection_keywords', '')
            if keywords:
                for keyword in keywords.split(', '):
                    keyword = keyword.strip()
                    if keyword:
                        keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        # 计算比率
        if stats['total_drugs'] > 0:
            stats['injection_rate'] = stats['injection_drugs'] / stats['total_drugs'] * 100
            stats['search_term_verification_rate'] = stats['verified_terms'] / stats['total_drugs'] * 100
        
        # 置信度分布
        stats['confidence_distribution'] = {
            'high': stats['high_confidence'],
            'medium': stats['medium_confidence'],
            'low': stats['low_confidence']
        }
        
        # 前10个最常见关键词
        sorted_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)
        stats['top_keywords'] = dict(sorted_keywords[:10])
        
        return stats
    
    def generate_html_report(self, output_file: str, include_charts: bool = True):
        """生成HTML报告"""
        stats = self.generate_statistics()
        
        html_content = self._create_html_template(stats, include_charts)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML报告已生成: {output_file}")
    
    def _create_html_template(self, stats: Dict[str, Any], include_charts: bool) -> str:
        """创建HTML模板"""
        chart_script = ""
        if include_charts:
            chart_script = self._generate_chart_scripts(stats)
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DailyMed搜索结果报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .chart-container {{
            margin: 30px 0;
            text-align: center;
        }}
        .data-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }}
        .data-table th, .data-table td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        .data-table th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .data-table tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .keywords-list {{
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }}
        .keyword-tag {{
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
        }}
        .section {{
            margin: 30px 0;
        }}
        .section h2 {{
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
    </style>
    {chart_script}
</head>
<body>
    <div class="container">
        <h1>DailyMed搜索结果分析报告</h1>
        
        <div class="section">
            <h2>📊 统计概览</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">{stats.get('total_drugs', 0)}</div>
                    <div class="stat-label">总药品数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats.get('injection_drugs', 0)}</div>
                    <div class="stat-label">注射剂数量</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats.get('high_confidence', 0)}</div>
                    <div class="stat-label">高置信度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{stats.get('search_term_verification_rate', 0):.1f}%</div>
                    <div class="stat-label">搜索词验证率</div>
                </div>
            </div>
        </div>
        
        {self._generate_charts_html(stats, include_charts)}
        
        <div class="section">
            <h2>🔍 常见注射剂关键词</h2>
            <div class="keywords-list">
                {self._generate_keywords_html(stats.get('top_keywords', {}))}
            </div>
        </div>
        
        <div class="section">
            <h2>📋 详细数据</h2>
            {self._generate_data_table_html()}
        </div>
        
        <div class="section">
            <p style="text-align: center; color: #7f8c8d; font-size: 0.9em;">
                报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </p>
        </div>
    </div>
</body>
</html>
        """
        return html
    
    def _generate_chart_scripts(self, stats: Dict[str, Any]) -> str:
        """生成图表脚本"""
        return """
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        """
    
    def _generate_charts_html(self, stats: Dict[str, Any], include_charts: bool) -> str:
        """生成图表HTML"""
        if not include_charts:
            return ""
        
        return f"""
        <div class="section">
            <h2>📈 数据可视化</h2>
            <div class="chart-container">
                <canvas id="confidenceChart" width="400" height="200"></canvas>
            </div>
            <script>
                const ctx = document.getElementById('confidenceChart').getContext('2d');
                new Chart(ctx, {{
                    type: 'doughnut',
                    data: {{
                        labels: ['高置信度', '中置信度', '低置信度'],
                        datasets: [{{
                            data: [{stats.get('high_confidence', 0)}, {stats.get('medium_confidence', 0)}, {stats.get('low_confidence', 0)}],
                            backgroundColor: ['#2ecc71', '#f39c12', '#e74c3c']
                        }}]
                    }},
                    options: {{
                        responsive: true,
                        plugins: {{
                            title: {{
                                display: true,
                                text: '置信度分布'
                            }}
                        }}
                    }}
                }});
            </script>
        </div>
        """
    
    def _generate_keywords_html(self, keywords: Dict[str, int]) -> str:
        """生成关键词HTML"""
        html = ""
        for keyword, count in keywords.items():
            html += f'<span class="keyword-tag">{keyword} ({count})</span>'
        return html
    
    def _generate_data_table_html(self) -> str:
        """生成数据表格HTML"""
        if not self.data:
            return "<p>没有数据可显示</p>"
        
        # 只显示前20条记录
        display_data = self.data[:20]
        
        html = '<table class="data-table">'
        html += '<thead><tr>'
        
        # 表头
        key_fields = ['name', 'confidence', 'is_injection', 'search_term_verified']
        headers = {'name': '药品名称', 'confidence': '置信度', 'is_injection': '是否注射剂', 'search_term_verified': '搜索词验证'}
        
        for field in key_fields:
            html += f'<th>{headers.get(field, field)}</th>'
        html += '</tr></thead><tbody>'
        
        # 数据行
        for drug in display_data:
            html += '<tr>'
            for field in key_fields:
                value = drug.get(field, '')
                if field == 'confidence':
                    value = {'high': '高', 'medium': '中', 'low': '低'}.get(value, value)
                elif field in ['is_injection', 'search_term_verified']:
                    value = '是' if value in ['True', True] else '否'
                html += f'<td>{value}</td>'
            html += '</tr>'
        
        html += '</tbody></table>'
        
        if len(self.data) > 20:
            html += f'<p style="text-align: center; margin-top: 10px;">显示前20条记录，共{len(self.data)}条</p>'
        
        return html
    
    def generate_markdown_report(self, output_file: str):
        """生成Markdown报告"""
        stats = self.generate_statistics()
        
        md_content = f"""# DailyMed搜索结果分析报告

## 📊 统计概览

| 指标 | 数值 |
|------|------|
| 总药品数 | {stats.get('total_drugs', 0)} |
| 注射剂数量 | {stats.get('injection_drugs', 0)} |
| 高置信度 | {stats.get('high_confidence', 0)} |
| 中置信度 | {stats.get('medium_confidence', 0)} |
| 低置信度 | {stats.get('low_confidence', 0)} |
| 搜索词验证率 | {stats.get('search_term_verification_rate', 0):.1f}% |

## 🔍 常见注射剂关键词

"""
        
        for keyword, count in stats.get('top_keywords', {}).items():
            md_content += f"- **{keyword}**: {count}次\n"
        
        md_content += f"""

## 📋 详细数据

| 药品名称 | 置信度 | 是否注射剂 | 搜索词验证 |
|----------|--------|------------|------------|
"""
        
        # 只显示前10条记录
        for drug in self.data[:10]:
            name = drug.get('name', '')[:50] + '...' if len(drug.get('name', '')) > 50 else drug.get('name', '')
            confidence = {'high': '高', 'medium': '中', 'low': '低'}.get(drug.get('confidence', ''), drug.get('confidence', ''))
            is_injection = '是' if drug.get('is_injection') in ['True', True] else '否'
            verified = '是' if drug.get('search_term_verified') in ['True', True] else '否'
            
            md_content += f"| {name} | {confidence} | {is_injection} | {verified} |\n"
        
        md_content += f"""

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        logger.info(f"Markdown报告已生成: {output_file}")
    
    def generate_summary(self) -> str:
        """生成简要摘要"""
        stats = self.generate_statistics()
        
        summary = f"""
DailyMed搜索结果摘要:
- 总共检查了 {stats.get('total_drugs', 0)} 个药品
- 找到 {stats.get('injection_drugs', 0)} 个注射剂
- 高置信度结果: {stats.get('high_confidence', 0)} 个
- 搜索词验证通过率: {stats.get('search_term_verification_rate', 0):.1f}%
        """.strip()
        
        return summary
