#!/usr/bin/env python3
"""
DailyMed核心搜索引擎

整合了所有精华功能的核心搜索引擎，包括：
- 高级搜索功能
- 注射剂智能识别
- DOSAGE AND ADMINISTRATION重点分析
- 搜索词验证
- 多种输出格式支持
"""

import urllib.request
import urllib.parse
import json
import time
import re
import html
import logging
import csv
from datetime import datetime
from typing import List, Dict, Any, Optional

try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

logger = logging.getLogger(__name__)

class DailyMedSearchEngine:
    """DailyMed搜索引擎核心类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.base_api_url = "https://dailymed.nlm.nih.gov/dailymed/services/v2"
        self.base_web_url = "https://dailymed.nlm.nih.gov/dailymed"
        self.headers = {
            'User-Agent': config.get('user_agent', 'DailyMed-CLI/1.0.0')
        }
        
        # 注射剂关键词
        self.injection_keywords = [
            # 给药途径
            'intravenous', 'intramuscular', 'subcutaneous', 'intravenously', 'intramuscularly',
            'iv', 'im', 'sc', 'sq', 'inject', 'injection', 'injectable',
            
            # 给药方式
            'bolus', 'infusion', 'infuse', 'parenteral', 'parenterally',
            
            # 包装和器具
            'vial', 'ampule', 'ampoule', 'syringe', 'needle',
            'single-dose vial', 'multi-dose vial', 'prefilled syringe',
            
            # 制剂描述
            'sterile solution', 'sterile suspension', 'solution for injection',
            'suspension for injection', 'powder for injection', 'concentrate for injection',
            
            # 给药指导
            'administer intravenously', 'administer intramuscularly', 
            'administer subcutaneously', 'administer by injection'
        ]
        
        # 统计信息
        self.stats = {
            'total_searched': 0,
            'total_found': 0,
            'high_confidence': 0,
            'medium_confidence': 0,
            'verified_terms': 0,
            'start_time': None,
            'end_time': None
        }
    
    def _make_request(self, url: str, accept_json: bool = False) -> Optional[str]:
        """发送HTTP请求"""
        headers = self.headers.copy()
        if accept_json:
            headers['Accept'] = 'application/json'
        
        req = urllib.request.Request(url, headers=headers)
        try:
            with urllib.request.urlopen(req, timeout=self.config.get('timeout', 30)) as response:
                return response.read().decode('utf-8')
        except Exception as e:
            logger.error(f"请求失败 {url}: {e}")
            return None
    
    def build_search_url(self, ingredient: str, search_type: str = "inactive", 
                        label_type: str = "human", page: int = 1, pagesize: int = 50) -> str:
        """构建搜索URL"""
        # 映射搜索类型
        type_mapping = {
            'active': 'ACTIVE_INGREDIENT',
            'inactive': 'INACTIVE_INGREDIENT',
            'drug_name': 'DRUG_NAME',
            'manufacturer': 'MANUFACTURER'
        }
        
        search_type_param = type_mapping.get(search_type, 'INACTIVE_INGREDIENT')
        
        if search_type_param and ingredient:
            query = f"{search_type_param}:({ingredient})"
        else:
            query = ingredient
        
        search_params = {
            'adv': '1',
            'labeltype': label_type,
            'query': query,
            'page': page,
            'pagesize': pagesize
        }
        
        url = f"{self.base_web_url}/search.cfm?" + urllib.parse.urlencode(search_params)
        return url
    
    def search_drugs_page(self, ingredient: str, search_type: str = "inactive", 
                         label_type: str = "human", page: int = 1, pagesize: int = 50) -> tuple:
        """搜索单页药品"""
        try:
            url = self.build_search_url(ingredient, search_type, label_type, page, pagesize)
            logger.debug(f"搜索URL: {url}")
            
            response_text = self._make_request(url)
            if not response_text:
                return [], 0
            
            # 解析搜索结果
            results = []
            
            # 查找包含setid的链接
            setid_pattern = r'/dailymed/drugInfo\.cfm\?setid=([a-f0-9-]+)'
            setid_matches = re.findall(setid_pattern, response_text)
            
            # 查找药品名称
            name_pattern = r'<a[^>]*href="[^"]*setid=[a-f0-9-]+"[^>]*>([^<]+)</a>'
            name_matches = re.findall(name_pattern, response_text, re.IGNORECASE)
            
            # 组合结果
            for i, setid in enumerate(setid_matches):
                name = html.unescape(name_matches[i].strip()) if i < len(name_matches) else f"Drug_{i+1}"
                results.append({
                    'name': name,
                    'setid': setid,
                    'url': f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
                })
            
            # 获取总结果数
            total_results = 0
            patterns = [
                r'(\d+)\s+results',
                r'of\s+(\d+)\s+',
                r'(\d+)\s+of\s+\d+',
                r'first\s+\d+\s+of\s+(\d+)'
            ]
            
            for pattern in patterns:
                result_match = re.search(pattern, response_text, re.IGNORECASE)
                if result_match:
                    total_results = int(result_match.group(1))
                    break
            
            logger.info(f"页面 {page}: 找到 {len(results)} 个药品，总计 {total_results} 个结果")
            return results, total_results
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return [], 0
    
    def get_drug_details(self, setid: str, ingredient: str, 
                        include_dosage: bool = True, include_full_content: bool = False) -> Optional[Dict]:
        """获取药品详细信息"""
        try:
            url = f"{self.base_web_url}/drugInfo.cfm?setid={setid}"
            response_text = self._make_request(url)
            if not response_text:
                return None
            
            info = {'setid': setid}
            
            # 提取基本信息
            title_match = re.search(r'<title>([^<]+)</title>', response_text, re.IGNORECASE)
            if title_match:
                info['title'] = html.unescape(title_match.group(1).strip())
            
            # 提取DOSAGE AND ADMINISTRATION部分
            if include_dosage:
                dosage_admin = self.extract_dosage_administration(response_text)
                info['dosage_administration'] = dosage_admin
            else:
                dosage_admin = ""
            
            # 验证搜索词是否在内容中
            verification_passed, found_terms = self.verify_search_term_in_content(response_text, ingredient)
            info['search_term_verified'] = verification_passed
            info['found_search_terms'] = found_terms
            
            # 分析注射剂指标
            drug_name = info.get('title', '')
            is_injection, injection_keywords, confidence = self.analyze_injection_indicators(
                dosage_admin, response_text, drug_name)
            info['is_injection'] = is_injection
            info['injection_keywords_found'] = injection_keywords
            info['confidence'] = confidence
            
            # 如果在DOSAGE AND ADMINISTRATION中找到注射剂关键词，标记为高置信度
            dosage_has_injection = any(keyword.lower() in dosage_admin.lower() 
                                     for keyword in self.injection_keywords) if dosage_admin else False
            info['dosage_has_injection_keywords'] = dosage_has_injection
            
            # 包含完整内容（可选）
            if include_full_content:
                info['full_content'] = response_text[:10000]  # 限制长度
            
            return info
            
        except Exception as e:
            logger.error(f"获取药品详情失败 (SETID: {setid}): {e}")
            return None
    
    def extract_dosage_administration(self, html_content: str) -> str:
        """提取DOSAGE AND ADMINISTRATION部分"""
        if not html_content:
            return ""
        
        # 查找DOSAGE AND ADMINISTRATION部分
        patterns = [
            r'DOSAGE\s+AND\s+ADMINISTRATION(.*?)(?=<h[1-6]|$)',
            r'dosage\s+and\s+administration(.*?)(?=<h[1-6]|$)',
            r'ADMINISTRATION\s+AND\s+DOSAGE(.*?)(?=<h[1-6]|$)',
            r'administration\s+and\s+dosage(.*?)(?=<h[1-6]|$)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content, re.IGNORECASE | re.DOTALL)
            if match:
                # 清理HTML标签
                content = re.sub(r'<[^>]+>', ' ', match.group(1))
                # 清理多余空白
                content = re.sub(r'\s+', ' ', content).strip()
                return content[:2000]  # 限制长度
        
        return ""
    
    def verify_search_term_in_content(self, html_content: str, search_term: str) -> tuple:
        """在药品详情中验证搜索词是否存在"""
        if not html_content or not search_term:
            return False, []
        
        # 清理HTML并转换为小写
        clean_content = re.sub(r'<[^>]+>', ' ', html_content).lower()
        search_terms = search_term.lower().split()
        
        found_terms = []
        for term in search_terms:
            if term in clean_content:
                found_terms.append(term)
        
        # 如果找到了所有搜索词，认为验证通过
        verification_passed = len(found_terms) == len(search_terms)
        
        return verification_passed, found_terms
    
    def analyze_injection_indicators(self, dosage_admin_text: str, full_content: str, drug_name: str = "") -> tuple:
        """分析注射剂指标"""
        if not dosage_admin_text and not full_content:
            return False, [], 'low'
        
        # 首先检查药品名称中的明显排除词
        drug_name_lower = drug_name.lower()
        strong_exclusions = ['tablet', 'capsule', 'cream', 'ointment', 'gel', 'lotion', 
                           'syrup', 'liquid', 'powder', 'suspension', 'drops', 'spray']
        
        # 如果药品名称包含明显的非注射剂词汇，需要更严格的判断
        has_exclusion_in_name = any(excl in drug_name_lower for excl in strong_exclusions)
        
        # 强注射剂指示词（在药品名称或DOSAGE部分出现）
        strong_injection_words = ['injection', 'injectable', 'intravenous', 'intramuscular', 
                                'subcutaneous', 'parenteral']
        
        # 检查药品名称中的强注射剂指示词
        name_has_strong_injection = any(word in drug_name_lower for word in strong_injection_words)
        
        # 分析DOSAGE AND ADMINISTRATION部分
        dosage_keywords = []
        if dosage_admin_text:
            dosage_text_lower = dosage_admin_text.lower()
            for keyword in self.injection_keywords:
                if keyword.lower() in dosage_text_lower:
                    dosage_keywords.append(keyword)
        
        # 分析完整内容
        analysis_text = (dosage_admin_text + " " + (full_content or "")).lower()
        found_keywords = []
        for keyword in self.injection_keywords:
            if keyword.lower() in analysis_text:
                found_keywords.append(keyword)
        
        # 检查DOSAGE部分是否明确提到口服给药
        oral_indicators = ['oral', 'orally', 'by mouth', 'swallow', 'teaspoonful', 'tablespoonful']
        dosage_has_oral = any(word in dosage_admin_text.lower() for word in oral_indicators) if dosage_admin_text else False
        
        # 判断逻辑和置信度
        confidence = 'low'
        is_injection = False
        
        if name_has_strong_injection:
            is_injection = True
            confidence = 'high'
        elif dosage_has_oral:
            # 如果DOSAGE部分明确提到口服，则不是注射剂
            is_injection = False
            confidence = 'low'
        elif has_exclusion_in_name:
            # 对于有排除词的药品，需要在DOSAGE部分找到明确的注射剂证据
            strong_dosage_evidence = any(word in dosage_admin_text.lower() 
                                       for word in strong_injection_words) if dosage_admin_text else False
            is_injection = strong_dosage_evidence and len(dosage_keywords) >= 3
            confidence = 'medium' if is_injection else 'low'
        else:
            # 对于没有排除词的药品，主要看DOSAGE部分的证据
            dosage_has_strong_injection = any(word in dosage_admin_text.lower() 
                                            for word in strong_injection_words) if dosage_admin_text else False
            is_injection = dosage_has_strong_injection or len(dosage_keywords) >= 3
            confidence = 'high' if dosage_has_strong_injection else 'medium' if is_injection else 'low'
        
        return is_injection, found_keywords, confidence

    def search(self, ingredient: str, search_type: str = "inactive",
               label_type: str = "human", filter_injections: bool = True,
               verify_terms: bool = True, confidence_filter: str = "all",
               max_drugs: int = 100, max_pages: Optional[int] = None,
               delay: float = 0.5, include_dosage: bool = True,
               include_full_content: bool = False) -> List[Dict]:
        """执行搜索"""

        self.stats['start_time'] = datetime.now()
        logger.info(f"开始搜索: {search_type.upper()}:({ingredient})")
        logger.info(f"过滤条件: {'注射剂' if filter_injections else '所有药品'}")
        logger.info(f"搜索词验证: {'启用' if verify_terms else '禁用'}")

        all_results = []
        filtered_results = []
        total_checked = 0
        page = 1

        # 首先获取总数
        _, total_count = self.search_drugs_page(ingredient, search_type, label_type, page=1, pagesize=50)
        logger.info(f"搜索到总计 {total_count} 个药品")

        if max_pages is None:
            max_pages = (total_count // 50) + 1

        if max_drugs is None:
            max_drugs = total_count

        while page <= max_pages and total_checked < max_drugs:
            logger.info(f"正在搜索第 {page} 页...")
            search_results, _ = self.search_drugs_page(ingredient, search_type, label_type,
                                                    page=page, pagesize=50)

            if not search_results:
                logger.warning(f"第 {page} 页没有找到结果")
                break

            for drug in search_results:
                if total_checked >= max_drugs:
                    break

                total_checked += 1
                logger.info(f"检查药品 {total_checked}/{min(max_drugs, total_count)}: {drug['name']}")

                # 获取药品详情
                drug_details = self.get_drug_details(
                    drug['setid'], ingredient, include_dosage, include_full_content)

                if drug_details:
                    # 如果启用搜索词验证，且验证失败，则跳过
                    if verify_terms and not drug_details.get('search_term_verified', False):
                        logger.warning(f"⚠️  搜索词验证失败: {drug['name']}")
                        continue

                    # 构建结果记录
                    drug_info = {
                        'name': drug['name'],
                        'setid': drug['setid'],
                        'url': drug['url'],
                        'title': drug_details.get('title', ''),
                        'dosage_administration': drug_details.get('dosage_administration', ''),
                        'is_injection': drug_details.get('is_injection', False),
                        'confidence': drug_details.get('confidence', 'low'),
                        'dosage_has_injection_keywords': drug_details.get('dosage_has_injection_keywords', False),
                        'injection_keywords': ', '.join(drug_details.get('injection_keywords_found', [])),
                        'search_term_verified': drug_details.get('search_term_verified', False),
                        'found_search_terms': ', '.join(drug_details.get('found_search_terms', []))
                    }

                    if include_full_content:
                        drug_info['full_content'] = drug_details.get('full_content', '')

                    all_results.append(drug_info)

                    # 应用过滤条件
                    should_include = True

                    # 注射剂过滤
                    if filter_injections and not drug_details.get('is_injection', False):
                        should_include = False

                    # 置信度过滤
                    if confidence_filter != 'all':
                        drug_confidence = drug_details.get('confidence', 'low')
                        if confidence_filter == 'high' and drug_confidence != 'high':
                            should_include = False
                        elif confidence_filter == 'medium' and drug_confidence not in ['high', 'medium']:
                            should_include = False

                    if should_include:
                        filtered_results.append(drug_info)
                        confidence = drug_details.get('confidence', 'low')
                        confidence_text = "高" if confidence == 'high' else "中" if confidence == 'medium' else "低"
                        logger.info(f"✅ 发现{'注射剂' if filter_injections else '药品'} (置信度: {confidence_text}): {drug['name']}")

                        if drug_details.get('dosage_administration'):
                            logger.info(f"   DOSAGE & ADMIN: {drug_details['dosage_administration'][:100]}...")

                        if drug_details.get('injection_keywords_found'):
                            keywords = drug_details['injection_keywords_found'][:5]  # 只显示前5个
                            logger.info(f"   关键词: {keywords}")

                # 更新统计
                self.stats['total_searched'] = total_checked
                if drug_details and drug_details.get('search_term_verified'):
                    self.stats['verified_terms'] += 1

                # 添加延迟避免请求过快
                time.sleep(delay)

            page += 1

        # 完成统计
        self.stats['end_time'] = datetime.now()
        self.stats['total_found'] = len(filtered_results)
        self.stats['high_confidence'] = sum(1 for r in filtered_results if r.get('confidence') == 'high')
        self.stats['medium_confidence'] = sum(1 for r in filtered_results if r.get('confidence') == 'medium')

        duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()

        logger.info(f"搜索完成！")
        logger.info(f"总共检查了 {total_checked} 个药品")
        logger.info(f"找到 {len(filtered_results)} 个结果")
        logger.info(f"高置信度: {self.stats['high_confidence']} 个")
        logger.info(f"中置信度: {self.stats['medium_confidence']} 个")
        logger.info(f"搜索用时: {duration:.1f} 秒")

        return filtered_results

    def save_results(self, results: List[Dict], output_path: str, format_type: str = 'csv'):
        """保存搜索结果"""
        try:
            if format_type == 'csv':
                self._save_csv(results, output_path)
            elif format_type == 'json':
                self._save_json(results, output_path)
            elif format_type == 'xlsx':
                self._save_xlsx(results, output_path)
            else:
                raise ValueError(f"不支持的输出格式: {format_type}")

            logger.info(f"结果已保存到: {output_path}")

        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise

    def _save_csv(self, results: List[Dict], output_path: str):
        """保存为CSV格式"""
        if not results:
            logger.warning("没有结果可保存")
            return

        with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = list(results[0].keys())
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for result in results:
                writer.writerow(result)

    def _save_json(self, results: List[Dict], output_path: str):
        """保存为JSON格式"""
        output_data = {
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_results': len(results),
                'statistics': self.stats
            },
            'results': results
        }

        with open(output_path, 'w', encoding='utf-8') as jsonfile:
            json.dump(output_data, jsonfile, indent=2, ensure_ascii=False)

    def _save_xlsx(self, results: List[Dict], output_path: str):
        """保存为Excel格式"""
        if not PANDAS_AVAILABLE:
            logger.error("需要安装 pandas 和 openpyxl 来支持 Excel 输出")
            logger.error("请运行: pip install pandas openpyxl")
            raise ImportError("pandas 未安装")

        try:
            df = pd.DataFrame(results)
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='搜索结果', index=False)

                # 添加统计信息工作表
                stats_df = pd.DataFrame([self.stats])
                stats_df.to_excel(writer, sheet_name='统计信息', index=False)
        except ImportError as e:
            logger.error(f"Excel输出需要额外依赖: {e}")
            raise

    def get_statistics(self) -> Dict:
        """获取搜索统计信息"""
        return self.stats.copy()
