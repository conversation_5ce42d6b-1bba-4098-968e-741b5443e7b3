#!/usr/bin/env python3
"""
DailyMed工具模块

提供配置管理、日志设置、数据验证等工具功能
"""

import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any

def setup_logging(level: int = logging.INFO):
    """设置日志配置"""
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(console_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)

def load_config(config_path: Path) -> Dict[str, Any]:
    """加载配置文件"""
    default_config = {
        "delay": 0.5,
        "max_retries": 3,
        "timeout": 30,
        "user_agent": "DailyMed-CLI/1.0.0",
        "output_dir": "./results",
        "cache_enabled": True,
        "cache_dir": "./cache"
    }
    
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
            # 合并配置
            default_config.update(user_config)
        except Exception as e:
            logging.warning(f"加载配置文件失败，使用默认配置: {e}")
    
    return default_config

def save_config(config: Dict[str, Any], config_path: Path):
    """保存配置文件"""
    try:
        # 确保目录存在
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    except Exception as e:
        logging.error(f"保存配置文件失败: {e}")
        raise

def validate_ingredient_name(ingredient: str) -> bool:
    """验证成分名称格式"""
    if not ingredient or not ingredient.strip():
        return False
    
    # 基本长度检查
    if len(ingredient.strip()) < 2:
        return False
    
    # 检查是否包含有效字符
    import re
    if not re.match(r'^[A-Za-z0-9\s\-\(\)\.]+$', ingredient):
        return False
    
    return True

def format_duration(seconds: float) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes < 1024:
        return f"{size_bytes}B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f}KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f}MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.1f}GB"

def create_progress_bar(current: int, total: int, width: int = 50) -> str:
    """创建进度条"""
    if total == 0:
        return "[" + "=" * width + "]"
    
    progress = current / total
    filled = int(width * progress)
    bar = "=" * filled + "-" * (width - filled)
    percentage = progress * 100
    
    return f"[{bar}] {percentage:.1f}% ({current}/{total})"

class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, total: int, description: str = "处理中"):
        self.total = total
        self.current = 0
        self.description = description
        self.logger = logging.getLogger(__name__)
    
    def update(self, increment: int = 1):
        """更新进度"""
        self.current += increment
        if self.current % 10 == 0 or self.current == self.total:
            progress_bar = create_progress_bar(self.current, self.total)
            self.logger.info(f"{self.description}: {progress_bar}")
    
    def finish(self):
        """完成进度"""
        self.current = self.total
        progress_bar = create_progress_bar(self.current, self.total)
        self.logger.info(f"{self.description}: {progress_bar} 完成!")

def sanitize_filename(filename: str) -> str:
    """清理文件名，移除不安全字符"""
    import re
    # 移除或替换不安全字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # 移除多余的空格和点
    filename = re.sub(r'\s+', '_', filename)
    filename = filename.strip('.')
    # 限制长度
    if len(filename) > 200:
        filename = filename[:200]
    return filename

def ensure_directory(path: Path):
    """确保目录存在"""
    try:
        path.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logging.error(f"创建目录失败 {path}: {e}")
        raise

def load_batch_config(config_file: str) -> Dict[str, Any]:
    """加载批量搜索配置"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 验证配置格式
        required_fields = ['searches']
        for field in required_fields:
            if field not in config:
                raise ValueError(f"配置文件缺少必需字段: {field}")
        
        # 验证搜索配置
        for i, search in enumerate(config['searches']):
            if 'ingredient' not in search:
                raise ValueError(f"搜索配置 {i+1} 缺少 ingredient 字段")
        
        return config
        
    except Exception as e:
        logging.error(f"加载批量配置失败: {e}")
        raise

def create_batch_config_template() -> Dict[str, Any]:
    """创建批量配置模板"""
    return {
        "description": "DailyMed批量搜索配置",
        "global_settings": {
            "max_drugs": 100,
            "delay": 0.5,
            "filter_injections": True,
            "verify_terms": True,
            "confidence_filter": "all",
            "output_format": "csv"
        },
        "searches": [
            {
                "name": "含酒精的注射剂",
                "ingredient": "ALCOHOL",
                "type": "inactive",
                "max_drugs": 100
            },
            {
                "name": "含苯甲醇的注射剂",
                "ingredient": "BENZYL ALCOHOL",
                "type": "inactive",
                "max_drugs": 50
            },
            {
                "name": "胰岛素注射剂",
                "ingredient": "INSULIN",
                "type": "active",
                "max_drugs": 30
            }
        ]
    }

def validate_search_parameters(params: Dict[str, Any]) -> bool:
    """验证搜索参数"""
    required_params = ['ingredient']
    
    for param in required_params:
        if param not in params or not params[param]:
            logging.error(f"缺少必需参数: {param}")
            return False
    
    # 验证成分名称
    if not validate_ingredient_name(params['ingredient']):
        logging.error(f"无效的成分名称: {params['ingredient']}")
        return False
    
    # 验证搜索类型
    valid_types = ['active', 'inactive', 'drug_name', 'manufacturer']
    if 'type' in params and params['type'] not in valid_types:
        logging.error(f"无效的搜索类型: {params['type']}")
        return False
    
    # 验证数值参数
    numeric_params = ['max_drugs', 'max_pages', 'delay']
    for param in numeric_params:
        if param in params:
            try:
                value = float(params[param])
                if value < 0:
                    logging.error(f"参数 {param} 不能为负数")
                    return False
            except (ValueError, TypeError):
                logging.error(f"参数 {param} 必须是数字")
                return False
    
    return True

def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    import platform
    import sys
    
    return {
        "platform": platform.platform(),
        "python_version": sys.version,
        "architecture": platform.architecture(),
        "processor": platform.processor(),
        "hostname": platform.node()
    }
