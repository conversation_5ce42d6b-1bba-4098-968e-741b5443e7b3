# DailyMed CLI - 专业的药品数据搜索和分析工具

一个功能强大的命令行工具，用于搜索DailyMed数据库中的药品信息，特别专注于注射剂的识别和过滤。

## ✨ 核心功能

- 🔍 **高级搜索**: 支持活性成分、非活性成分、药品名称、制造商等多种搜索类型
- 💉 **智能注射剂识别**: 基于DOSAGE AND ADMINISTRATION部分的高精度识别
- 📊 **多格式输出**: 支持CSV、JSON、Excel格式
- 📈 **详细报告**: 生成HTML和Markdown格式的分析报告
- 🔄 **批量搜索**: 支持配置文件驱动的批量搜索任务
- ✅ **数据验证**: 验证搜索结果的完整性和准确性
- ⚙️ **配置管理**: 灵活的配置选项和参数调整

## 🚀 快速开始

### 安装要求
- Python 3.6+
- 标准库（无需额外安装包）
- 可选：pandas + openpyxl（用于Excel输出）

### 基本使用

```bash
# 使脚本可执行
chmod +x dailymed

# 显示帮助信息
./dailymed --help

# 基础搜索：含酒精的注射剂
./dailymed search --ingredient ALCOHOL --filter-injections

# 生成HTML报告
./dailymed report --input results.csv --format html
```

## 📋 主要命令

### 1. search - 搜索药品
```bash
# 搜索含酒精的注射剂
./dailymed search --ingredient ALCOHOL --filter-injections --max-drugs 100

# 搜索含苯甲醇的高置信度注射剂
./dailymed search --ingredient "BENZYL ALCOHOL" --filter-injections --confidence high

# 搜索胰岛素注射剂（活性成分）
./dailymed search --ingredient INSULIN --type active --filter-injections
```

### 2. batch - 批量搜索
```bash
# 执行批量搜索
./dailymed batch --config-file example_batch_config.json --output-dir results/
```

### 3. report - 生成报告
```bash
# 生成HTML分析报告
./dailymed report --input results.csv --format html --output report.html

# 生成Markdown报告
./dailymed report --input results.csv --format markdown
```

### 4. config - 配置管理
```bash
# 显示当前配置
./dailymed config --show

# 设置请求延迟
./dailymed config --set delay=1.0
```

### 5. validate - 数据验证
```bash
# 验证搜索结果
./dailymed validate --input results.csv --check-urls
```

## 注射剂识别逻辑

工具使用以下逻辑来识别注射剂：

### 强注射剂指示词（药品名称中）
- injection, injectable
- intravenous, intramuscular, subcutaneous

### 弱注射剂指示词（药品名称中）
- solution, vial, ampule, ampoule

### 内容关键词
- iv, im, sc, sq
- bolus, infusion, parenteral
- sterile solution, sterile suspension
- single-dose vial, multi-dose vial

### 排除词汇
- tablet, capsule, cream, ointment
- gel, lotion, syrup, liquid

### 判断规则
1. 如果药品名称包含强注射剂指示词且无排除词 → 注射剂
2. 如果药品名称包含弱注射剂指示词且无排除词且内容有注射剂关键词 → 注射剂
3. 其他情况 → 非注射剂

## 输出文件格式

生成的CSV文件包含以下列：

| 列名 | 描述 |
|------|------|
| name | 药品名称 |
| setid | 药品唯一标识符 |
| url | 药品详情页面URL |
| title | 完整标题 |
| dosage_form | 剂型 |
| is_injection | 是否为注射剂 (True/False) |
| injection_keywords | 找到的注射剂关键词 |
| exclusion_keywords | 找到的排除关键词 |

## 性能优化建议

1. **控制搜索范围**: 使用 `--max-drugs` 限制检查的药品数量
2. **调整请求间隔**: 使用 `--delay` 设置请求间隔，避免被服务器限制
3. **分批处理**: 对于大量数据，建议分批次处理

## 示例脚本

运行 `usage_examples.py` 查看更多使用示例：

```bash
python3 usage_examples.py
```

## 注意事项

1. **请求频率**: 工具默认在每个请求之间等待0.5秒，避免对DailyMed服务器造成过大压力
2. **网络连接**: 需要稳定的网络连接访问DailyMed网站
3. **数据准确性**: 注射剂识别基于关键词匹配，可能存在误判，建议人工复核重要结果
4. **法律合规**: 请遵守DailyMed的使用条款和相关法律法规

## 故障排除

### 常见问题

1. **连接超时**: 检查网络连接，可以增加 `--delay` 参数
2. **没有找到注射剂**: 尝试增加 `--max-drugs` 参数扩大搜索范围
3. **搜索结果为空**: 检查搜索词拼写，尝试使用不同的搜索类型

### 调试模式

如需查看详细的调试信息，可以修改脚本中的日志级别：

```python
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
```

## 许可证

本工具仅供学习和研究使用。使用时请遵守相关法律法规和DailyMed的使用条款。

## 更新日志

- v1.0: 初始版本，支持基本的搜索和过滤功能
- v1.1: 改进注射剂识别逻辑，提高准确性
- v1.2: 添加更多搜索类型支持，优化性能
